#!/usr/bin/env bash

usage() {
    echo "Usage: $0 [-r] [--force-team TEAM_NAME] [-h] [--base-url BASE_URL] dest_dir"
    echo "  -r: 递归安装"
    echo "  --force-team: 强制使用指定团队"
    echo "  -h: 显示帮助信息"
    echo "  --base-url: 指定git origin的base url"
    exit 1
}

get_git_origin_url() {
    local input_path=$1
    local origin_url=$(git -C "$input_path" remote get-url origin 2>/dev/null)
    if [ -z "$origin_url" ]; then
        echo "无法读取origin url" >&2
        exit 1
    fi
    echo "$origin_url"
}

get_target_from_git_origin_url() {
    local url=$1
    local base_url=$2
    local target_path=""
    if [ -n "$base_url" ]; then
        target_path=$(echo "$url" | sed "s#^$base_url##")
    else
        target_path=$(echo "$url" | sed 's#^[^:]*://[^/]*/*##')
    fi
    echo "$target_path"
}

get_team_from_target_path() {
    local target_path=$1
    local team_name=$(cat $script_dir/targets/$target_path/target 2>/dev/null)
    echo "$team_name"
}

get_real_git_path() {
    local input_path=$1
    local real_path=$(git -C "$input_path" rev-parse --absolute-git-dir 2>/dev/null)
    
    if [ -z "$real_path" ]; then
        echo "无法读取git路径" >&2
        exit 1
    fi

    # 检查是否是 Git worktree
    if [ -f "$real_path/commondir" ]; then
        # 读取公共目录路径
        local common_dir=$(cat "$real_path/commondir")
        
        # 处理相对路径
        if [[ "$common_dir" != /* ]]; then
            common_dir=$(cd "$real_path" && cd "$common_dir" && pwd)
        fi
        
        # 更新真实路径为公共目录
        real_path="$common_dir"
    fi

    echo "$real_path"
}

recursive=false
force_team=""
base_url=""

while [ $# -gt 0 ]; do
    case $1 in
        -r)
            recursive=true
            ;;
        --force-team)
            shift
            force_team=$1
            ;;
        -h|--help)
            usage
            ;;
        --base-url)
            shift
            base_url=$1
            ;;
        *)
            # 只接受一个目录参数
            if [ -n "$dest_dir" ]; then
                echo "参数过多" >&2
                usage
            fi
            # 禁止`-`开头的参数
            if [[ $1 == -* ]]; then
                echo "无效的参数: $1" >&2
                usage
            fi
            
            dest_dir=$1
            ;;
    esac
    shift
done

if [ -z "$dest_dir" ]; then
    usage
fi

script_dir=$(dirname "$(readlink -f "$0")")

if $recursive; then
    echo "----------------------------"
    echo "开始递归安装git hooks到$dest_dir"
    for d in $(find "$dest_dir" -type d,f,l -name '.git'); do
        $0 --force-team "$force_team" --base-url "$base_url" "$(dirname "$d")"
    done
    exit 0
fi

echo "----------------------------"
echo "开始安装git hooks到$dest_dir"

if [ -n "$force_team" ]; then
    echo "    强制使用团队: $force_team"
    team_name=$force_team
else
    origin_url=$(get_git_origin_url "$dest_dir")
    echo "    origin_url: $origin_url"
    target_path=$(get_target_from_git_origin_url "$origin_url" "$base_url")
    echo "    target_path: $target_path"
    if [ -z "$target_path" ]; then
        echo "无法获取target路径" >&2
        exit 1
    fi
    team_name=$(get_team_from_target_path "$target_path")
    echo "    team_name: $team_name"
    if [ -z "$team_name" ]; then
        echo "无法获取团队名称" >&2
        exit 1
    fi
    if [ "$team_name" = "-" ]; then
        echo "跳过安装"
        exit 0
    fi
fi

src_hook_path=$script_dir/teams/$team_name
echo "    using hooks from $src_hook_path"

target_git_path=$(get_real_git_path "$dest_dir")
echo "    target_git_path: $target_git_path"

if [ -z "$target_git_path" ]; then
    echo "无法获取目标git路径" >&2
    exit 1
fi

target_hook_path=$target_git_path/hooks

find $src_hook_path -maxdepth 1 -type f -exec cp {} $target_hook_path \; || {
    echo "复制hooks失败" >&2
    exit 1
}

echo "已安装$team_name团队的git hooks到$target_hook_path"