#!/bin/bash

# commit-msg hook 测试脚本
# 用法: ./run_tests.sh [选项] [测试文件...]
#
# 选项:
#   -v, --verbose    详细模式，显示更多测试信息
#   -t, --test FILE  指定运行特定的测试文件（可多次使用）
#   -h, --help       显示此帮助信息
#
# Git状态测试支持:
# 测试文件可以包含git状态标识来模拟不同的git操作状态
# 文件命名格式: XXX-description.state.accept/deny.txt
# 支持的状态:
#   - cherry-pick: 模拟cherry-pick操作状态
#   - rebase: 模拟rebase操作状态
#   - merge: 模拟merge操作状态
#   - revert: 模拟revert操作状态
#   - bisect: 模拟bisect操作状态
#   - worktree: 模拟worktree环境
# 例如: 025-test.cherry-pick.accept.txt 会在cherry-pick状态下测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认选项
VERBOSE=false
SPECIFIC_TESTS=()

# 计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 显示帮助信息
show_help() {
    echo "commit-msg Hook 测试脚本"
    echo ""
    echo "用法: $0 [选项] [测试文件...]"
    echo ""
    echo "选项:"
    echo "  -v, --verbose    详细模式，显示更多测试信息"
    echo "  -t, --test FILE  指定运行特定的测试文件（可多次使用）"
    echo "  -h, --help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 运行所有测试"
    echo "  $0 -v                        # 详细模式运行所有测试"
    echo "  $0 -t 001-basic.accept.txt   # 运行指定测试文件"
    echo "  $0 -v -t test1.txt -t test2.txt  # 详细模式运行多个指定测试"
    echo ""
    echo "测试文件命名规则:"
    echo "  XXX-description.accept.txt   # 期望通过的测试"
    echo "  XXX-description.deny.txt     # 期望失败的测试"
    echo "  XXX-description.{code}.txt   # 期望特定退出码的测试"
    echo "  XXX-description.state.accept/deny.txt  # 特定git状态下的测试"
    echo ""
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -t|--test)
                if [[ -z "$2" ]]; then
                    echo -e "${RED}错误: -t/--test 选项需要指定测试文件${NC}" >&2
                    exit 1
                fi
                SPECIFIC_TESTS+=("$2")
                shift 2
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                echo -e "${RED}错误: 未知选项 $1${NC}" >&2
                echo "使用 -h 或 --help 查看帮助信息" >&2
                exit 1
                ;;
            *)
                # 位置参数作为测试文件
                SPECIFIC_TESTS+=("$1")
                shift
                ;;
        esac
    done
}

# 解析命令行参数
parse_args "$@"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CURRENT_DIR="$(pwd)"
TEAM_DIR="$(dirname "$SCRIPT_DIR")"
COMMIT_MSG_HOOK="$TEAM_DIR/commit-msg"

echo "========================================"
echo "commit-msg Hook 测试套件"
echo "========================================"
echo "测试目录: $SCRIPT_DIR"
echo "Hook脚本: $COMMIT_MSG_HOOK"
if [ "$VERBOSE" = true ]; then
    echo -e "详细模式: ${BLUE}启用${NC}"
    if [ ${#SPECIFIC_TESTS[@]} -gt 0 ]; then
        echo -e "指定测试: ${BLUE}${SPECIFIC_TESTS[*]}${NC}"
    fi
fi
echo ""

# 检查commit-msg脚本是否存在
if [ ! -f "$COMMIT_MSG_HOOK" ]; then
    echo -e "${RED}错误: commit-msg hook 不存在: $COMMIT_MSG_HOOK${NC}"
    exit 1
fi

# 确保commit-msg脚本可执行
chmod +x "$COMMIT_MSG_HOOK"

# 创建临时目录用于测试
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

# 初始化临时git仓库
cd "$TEMP_DIR"
git init --quiet
git config user.name "Test User"
git config user.email "<EMAIL>"

# 创建一个初始提交
echo "test" > test.txt
git add test.txt
git commit -m "Initial commit" --quiet

echo "开始运行测试..."
echo ""

git_dir="${TEMP_DIR}/.git"
target_dir="$TEMP_DIR"

setup_git_state() {
    local state="$1"
    
    # 清理所有可能的状态文件
    rm -f "$git_dir"/{CHERRY_PICK_HEAD,MERGE_HEAD,REVERT_HEAD,BISECT_LOG,BISECT_START,REBASE_HEAD}
    rm -rf "$git_dir"/{rebase-merge,rebase-apply}
    target_dir="$TEMP_DIR"

    case "$state" in
        "cherry-pick")
            echo "dummy_commit_hash" > "$git_dir/CHERRY_PICK_HEAD"
            ;;
        "rebase")
            mkdir -p "$git_dir/rebase-merge"
            echo "refs/heads/dummy" > "$git_dir/rebase-merge/head-name"
            echo "dummy_commit" > "$git_dir/rebase-merge/onto"
            ;;
        "interactive-rebase")
            mkdir -p "$git_dir/rebase-apply"
            echo "dummy" > "$git_dir/rebase-apply/head-name"
            ;;
        "merge")
            echo "dummy_commit_hash" > "$git_dir/MERGE_HEAD"
            echo "Merge message" > "$git_dir/MERGE_MSG"
            ;;
        "revert")
            echo "dummy_commit_hash" > "$git_dir/REVERT_HEAD"
            ;;
        "bisect")
            echo "git bisect start" > "$git_dir/BISECT_LOG"
            echo "dummy_commit" > "$git_dir/BISECT_START"
            ;;
        "worktree")
            local new_worktree_dir=$(mktemp -d)
            echo "gitdir: $TEMP_DIR/.git" > "$new_worktree_dir/.git"
            target_dir="$new_worktree_dir"
            ;;
    esac
}

# 解析测试文件名中的git状态
parse_git_state() {
    local test_file="$1"
    local filename=$(basename "$test_file" .txt)

    # 检查文件名中是否包含git状态标识
    # 格式: XXX-description.state.accept/deny.txt
    # 例如: 025-cherry-pick-test.cherry-pick.accept.txt
    if [[ "$filename" =~ \.(cherry-pick|rebase|interactive-rebase|merge|revert|bisect|worktree|normal)\.(accept|deny)$ ]]; then
        echo "${BASH_REMATCH[1]}"
    else
        echo "normal"
    fi
}

# 运行测试函数
run_test() {
    local test_file="$1"
    local test_name=$(basename "$test_file" .txt)
    local expected_result=""
    local expected_exit_code=""
    local git_state=""

    # 解析git状态
    git_state=$(parse_git_state "$test_file")
    cd "$target_dir"

    # 根据文件名判断期望结果和错误码
    if [[ "$test_file" == *.accept.txt ]]; then
        expected_result="accept"
        expected_exit_code=0
    elif [[ "$test_file" == *.deny.txt ]]; then
        expected_result="deny"
    elif [[ "$test_file" =~ \.([0-9]+)\.txt$ ]]; then
        # 新格式：XXX-description.{errorcode}.txt
        expected_exit_code="${BASH_REMATCH[1]}"
        if [ "$expected_exit_code" -eq 0 ]; then
            expected_result="accept"
        else
            expected_result="deny-code"
        fi
    else
        echo -e "${YELLOW}跳过: $test_name (未知测试类型)${NC}"
        return
    fi

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    # 详细模式：显示测试开始信息
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}开始测试:${NC} $test_name"
        echo -e "${BLUE}  测试文件:${NC} $test_file"
        echo -e "${BLUE}  期望结果:${NC} $expected_result"
        if [ -n "$expected_exit_code" ]; then
            echo -e "${BLUE}  期望退出码:${NC} $expected_exit_code"
        fi
        echo -e "${BLUE}  Git状态:${NC} $git_state"

        # 显示测试消息内容
        echo -e "${BLUE}  测试消息内容:${NC}"
        sed 's/^/    /' "$test_file"
        echo ""
    fi

    # 设置git状态
    setup_git_state "$git_state"

    # 显示当前测试的git状态（非详细模式）
    if [ "$VERBOSE" = false ] && [ "$git_state" != "normal" ]; then
        echo -n -e "${YELLOW}[${git_state}]${NC} "
    fi

    # 复制测试消息到临时文件
    local temp_msg="$target_dir/commit_msg_test"
    cp "$test_file" "$temp_msg"

    # 运行commit-msg hook
    local exit_code=0
    local hook_output=""
    local start_time=$(date +%s.%N)
    hook_output=$("$COMMIT_MSG_HOOK" "$temp_msg" 2>&1) || exit_code=$?
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0")

    # 详细模式：显示执行信息
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}  实际退出码:${NC} $exit_code"
        if [ -n "$hook_output" ]; then
            echo -e "${BLUE}  Hook输出:${NC}"
            echo "$hook_output" | sed 's/^/    /'
        fi
        if [ $exit_code -ge 100 ]; then
            echo -e "${BLUE}  自动换行结果:${NC}"
            sed 's/^/    /' "$temp_msg.bak"
        fi
        if command -v bc >/dev/null 2>&1; then
            printf "${BLUE}  执行时间:${NC} %.3f秒\n" "$duration"
        fi
        echo ""
    fi

    # 检查结果
    local test_passed=false
    if [ "$expected_result" = "accept" ]; then
        if [ $exit_code -eq $expected_exit_code ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            test_passed=true
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (期望退出码 $expected_exit_code，实际 $exit_code)"
            if [ -n "$hook_output" ] && [ "$VERBOSE" = false ]; then
                echo -e "${RED}    错误信息: $hook_output${NC}"
            fi
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    elif [ "$expected_result" = "deny-code" ]; then
        if [ $exit_code -eq $expected_exit_code ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name (错误码: $exit_code)"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            test_passed=true
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (期望退出码 $expected_exit_code，实际 $exit_code)"
            if [ -n "$hook_output" ] && [ "$VERBOSE" = false ]; then
                echo -e "${RED}    错误信息: $hook_output${NC}"
            fi
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    elif [ "$expected_result" = "deny" ]; then
        if [ $exit_code -ne 0 ]; then
            echo -e "${GREEN}✓ PASS${NC}: $test_name (错误码: $exit_code)"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            test_passed=true
        else
            echo -e "${RED}✗ FAIL${NC}: $test_name (错误码: $exit_code)"
            if [ -n "$hook_output" ] && [ "$VERBOSE" = false ]; then
                echo -e "${RED}    错误信息: $hook_output${NC}"
            fi
            FAILED_TESTS=$((FAILED_TESTS + 1))
        fi
    else
        echo -e "${YELLOW}跳过: $test_name (未知测试类型)${NC}"
    fi

    # 详细模式：显示测试结束分隔线
    if [ "$VERBOSE" = true ]; then
        echo -e "${BLUE}----------------------------------------${NC}"
        echo ""
    fi

    # 恢复正常git状态
    setup_git_state "normal"
}

# 运行指定测试文件
run_specific_tests() {
    for test_file in "${SPECIFIC_TESTS[@]}"; do
        echo "运行指定测试: $test_file"
        test_file_path="$CURRENT_DIR/$test_file"
        if [ -f "$test_file_path" ]; then
            run_test "$test_file_path"
        else
            echo -e "${RED}错误: 测试文件不存在: $test_file${NC}"
            exit 1
        fi
    done
}

if [ ${#SPECIFIC_TESTS[@]} -gt 0 ]; then
    run_specific_tests
else
    # 运行所有测试文件
    for test_file in "$SCRIPT_DIR"/*.txt; do
        if [ -f "$test_file" ]; then
            run_test "$test_file"
        fi
    done
fi

echo ""
echo "========================================"
echo "测试结果汇总"
echo "========================================"
echo "总测试数: $TOTAL_TESTS"
echo -e "通过: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}所有测试通过! ✓${NC}"
    exit 0
else
    echo -e "${RED}有 $FAILED_TESTS 个测试失败! ✗${NC}"
    exit 1
fi
